{"name": "boplatform", "version": "0.0.1", "author": "wang<PERSON><PERSON>@myhexin.com", "private": true, "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider umi dev", "build": "cross-env BUILD=dev NODE_OPTIONS=--openssl-legacy-provider umi build", "build:prod": "cross-env BUILD=prod NODE_OPTIONS=--openssl-legacy-provider umi build", "test": "umi test", "lint:es": "eslint --ext .js src mock tests", "lint:ts": "tslint \"src/**/*.ts\" \"src/**/*.tsx\"", "uploadTest": "upload --name boplatform --user whw", "gen": "node ./src/tools/autoGenerateCode.js"}, "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^3.19.5", "braft-editor": "^2.3.9", "clipboard": "^2.0.8", "currency.js": "^2.0.4", "dva": "^2.6.0-beta.6", "enquire.js": "^2.1.6", "moo-css-base": "^0.9.1", "nanoid": "^4.0.2", "path-to-regexp": "^3.0.0", "react": "^16.8.6", "react-beautiful-dnd": "^13.1.0", "react-dnd": "^14.0.2", "react-dnd-html5-backend": "^14.0.0", "react-dom": "^16.8.6", "store": "^2.0.12", "xlsx": "^0.16.8", "@king-fisher/crm-amis": "^0.0.2", "@king-fisher/amis-adapter": "^0.0.2"}, "devDependencies": {"@types/classnames": "^2.2.9", "@types/jest": "^23.3.12", "@types/react": "^16.7.18", "@types/react-dom": "^16.0.11", "@types/react-test-renderer": "^16.0.3", "axios": "^0.19.0", "babel-eslint": "^9.0.0", "classnames": "^2.2.6", "core-decorators": "^0.20.0", "cross-env": "^7.0.3", "currency.js": "^2.0.4", "eslint": "^5.4.0", "eslint-config-umi": "^1.4.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-react": "^7.11.1", "form-render": "0.8.1", "fundcharts": "^0.9.2", "husky": "^0.14.3", "lint-staged": "^7.2.2", "moment": "^2.24.0", "monaco-editor": "^0.20.0", "react-markdown": "^4.3.1", "react-test-renderer": "^16.7.0", "tslint": "^5.12.0", "tslint-eslint-rules": "^5.4.0", "tslint-react": "^3.6.0", "umi": "^2.7.0", "umi-plugin-react": "^1.8.0", "umi-types": "^0.3.0"}, "lint-staged": {"*.{ts,tsx}": ["tslint --fix", "git add"], "*.{js,jsx}": ["eslint --fix", "git add"]}, "engines": {"node": ">=8.0.0"}}